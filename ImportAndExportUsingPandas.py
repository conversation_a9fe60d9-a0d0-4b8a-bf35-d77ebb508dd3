# import and export data using pandas
import pandas as pd

# load the data
data = {"Name": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        "Age": [24, 13, 53, 33]}
data_frame = pd.DataFrame(data)

# export the data
data_frame = (data_frame.to_csv("data.csv", sep=",", index=False))

# import the data
data_frame_imported = pd.read_csv("data.csv")

print("Imported data:\n", pd.DataFrame(data_frame_imported))