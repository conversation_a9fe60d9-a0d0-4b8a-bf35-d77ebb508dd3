from sklearn.linear_model import LinearRegression
import numpy as np
import matplotlib.pyplot as plt

X = np.array([2,3,4,5,6,7])
y = np.array([50,55,60,65,70,75])

X = X.reshape(-1,1)

model = LinearRegression()
model.fit(X,y)

y_pred = model.predict([[6]])
print(y_pred)

# Visualization
plt.scatter(X, y, color='blue', label='Data points')
plt.plot(X, model.predict(X), color='red', label='Regression line')
plt.xlabel('X')
plt.ylabel('y')
plt.title('Simple Linear Regression')
plt.legend()
plt.show()