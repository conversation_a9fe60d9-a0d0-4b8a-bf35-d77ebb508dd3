# Simple Linear regression model
from sklearn.linear_model import LinearRegression
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split


# Collection of dataset
X = [[2],[3],[4],[5],[6],[7]]
y = [50,55,60,65,70,75]

# split the dataset
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

print("X_train:",X_train)
print("X_test:",X_test)
print("y_train:",y_train)
print("y_test:",y_test)

# model training
model = LinearRegression()
model.fit(X_train,y_train)

# prediction
y_pred = model.predict(X_test)
print(y_pred)

