from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
import numpy as np
import matplotlib.pyplot as plt

# Example dataset: [size (sq ft), number of bedrooms]/
X = [[1500, 3], [1600, 3], [1700, 3], [1800, 4], [1900, 4], [2000, 4], [2100, 5], [2200, 5]]
y = [300000, 320000, 340000, 360000, 380000, 400000, 420000, 440000]

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

model = LinearRegression()
model.fit(X_train, y_train)

y_pred = model.predict(X_test)
print("X_train:", X_train)
print("X_test:", X_test)
print("y_train:", y_train)
print("y_test:", y_test)
print("Predicted prices:", y_pred) 